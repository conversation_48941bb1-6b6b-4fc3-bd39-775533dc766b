-- 修改用户表的created_at字段为DATE类型
-- 迁移脚本：将created_at从DATETIME改为DATE格式

USE `user_db`;

-- 1. 备份现有数据（可选）
-- CREATE TABLE user_backup AS SELECT * FROM user;

-- 2. 添加新的临时字段
ALTER TABLE `user` ADD COLUMN `created_at_new` DATE COMMENT '创建日期(新字段)';

-- 3. 将现有的datetime数据转换为date格式并填充到新字段
UPDATE `user` SET `created_at_new` = DATE(`created_at`);

-- 4. 删除原字段
ALTER TABLE `user` DROP COLUMN `created_at`;

-- 5. 重命名新字段为原字段名
ALTER TABLE `user` CHANGE COLUMN `created_at_new` `created_at` DATE NOT NULL COMMENT '创建日期';

-- 6. 为了保持数据完整性，我们需要为现有记录设置默认的创建日期
-- 如果有空值，设置为当前日期
UPDATE `user` SET `created_at` = CURDATE() WHERE `created_at` IS NULL;

-- 7. 验证数据迁移结果
SELECT 
    id, 
    username, 
    created_at,
    updated_at
FROM `user` 
ORDER BY id;

-- 8. 显示表结构确认修改
DESCRIBE `user`;
