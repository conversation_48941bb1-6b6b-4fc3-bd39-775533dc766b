-- V2__add_product_audit_fields.sql
-- 为商品表添加审核相关字段
-- 执行时间：2024年

USE `product_db`;

-- 添加审核状态字段
ALTER TABLE `product` ADD COLUMN `audit_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '审核状态：pending-待审核 approved-已通过 rejected-已拒绝' AFTER `status`;

-- 添加审核意见字段
ALTER TABLE `product` ADD COLUMN `audit_reason` TEXT COMMENT '审核意见' AFTER `audit_status`;

-- 添加审核时间字段
ALTER TABLE `product` ADD COLUMN `audit_time` DATETIME DEFAULT NULL COMMENT '审核时间' AFTER `audit_reason`;

-- 添加审核员ID字段
ALTER TABLE `product` ADD COLUMN `auditor_id` BIGINT(20) DEFAULT NULL COMMENT '审核员ID' AFTER `audit_time`;

-- 添加索引以提高查询性能
CREATE INDEX `idx_audit_status` ON `product`(`audit_status`);
CREATE INDEX `idx_audit_time` ON `product`(`audit_time`);
CREATE INDEX `idx_auditor` ON `product`(`auditor_id`);

-- 更新现有数据，将所有商品设置为待审核状态
UPDATE `product` SET `audit_status` = 'pending' WHERE `audit_status` IS NULL OR `audit_status` = '';

-- 添加外键约束（如果需要关联用户表）
-- 注意：由于用户表在不同的数据库中，这里暂时不添加外键约束
-- 如果需要，可以在应用层面进行关联验证
-- ALTER TABLE `product` ADD CONSTRAINT `fk_product_auditor` FOREIGN KEY (`auditor_id`) REFERENCES `user_db`.`user`(`id`);

-- 创建商品审核历史表（可选，用于记录审核历史）
CREATE TABLE IF NOT EXISTS `product_audit_history` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '审核历史ID',
    `product_id` bigint(20) NOT NULL COMMENT '商品ID',
    `audit_status` varchar(20) NOT NULL COMMENT '审核状态：pending-待审核 approved-已通过 rejected-已拒绝',
    `audit_reason` text COMMENT '审核意见',
    `auditor_id` bigint(20) DEFAULT NULL COMMENT '审核员ID',
    `audit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '审核时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_product` (`product_id`),
    KEY `idx_auditor` (`auditor_id`),
    KEY `idx_audit_time` (`audit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品审核历史表';
