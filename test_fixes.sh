#!/bin/bash

# 前端修复功能验证脚本
# 测试修复的API功能

echo "🔍 开始验证前端修复功能..."
echo "=================================="

BASE_URL="http://localhost:8081/api/v1"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local name="$1"
    local url="$2"
    local method="${3:-GET}"
    local data="$4"
    
    echo -n "测试 $name ... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" "$url")
    else
        response=$(curl -s -w "%{http_code}" -X "$method" -H "Content-Type: application/json" -d "$data" "$url")
    fi
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 通过${NC}"
        return 0
    else
        echo -e "${RED}❌ 失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
        return 1
    fi
}

echo "1. 测试用户管理API"
echo "-------------------"

# 测试用户列表
test_api "用户列表" "$BASE_URL/users?page=1&pageSize=10"

# 测试用户搜索（按时间）
test_api "按时间搜索用户" "$BASE_URL/users?page=1&pageSize=10&startDate=2025-05-29&endDate=2025-05-30"

# 测试用户状态更新
echo -n "测试用户状态更新 ... "
response=$(curl -s -w "%{http_code}" -X PUT -H "Content-Type: application/json" -d '{"status":1}' "$BASE_URL/users/status/2")
http_code="${response: -3}"
if [ "$http_code" = "200" ]; then
    echo -e "${GREEN}✅ 通过${NC}"
else
    echo -e "${YELLOW}⚠️  需要认证 (HTTP $http_code)${NC}"
fi

echo ""
echo "2. 测试角色管理API"
echo "-------------------"

# 测试角色列表
test_api "角色列表" "$BASE_URL/roles?page=1&pageSize=10"

# 测试权限列表
test_api "权限列表" "$BASE_URL/permissions?page=1&pageSize=10"

echo ""
echo "3. 测试财务管理API"
echo "-------------------"

# 测试交易记录
test_api "交易记录" "$BASE_URL/finance/transactions?page=1&pageSize=10"

# 测试财务报表
test_api "日报表" "$BASE_URL/finance/report/daily"
test_api "月报表" "$BASE_URL/finance/report/monthly"

echo ""
echo "4. 测试系统健康状态"
echo "-------------------"

test_api "系统健康检查" "http://localhost:8081/health"

echo ""
echo "=================================="
echo "🎉 API验证完成！"
echo ""
echo "📋 下一步验证："
echo "1. 打开浏览器访问: http://localhost:3002"
echo "2. 测试用户管理的搜索、状态切换、删除功能"
echo "3. 测试角色管理的权限配置功能"
echo "4. 测试财务管理的交易记录和报表功能"
echo ""
echo "💡 提示："
echo "- 前端已在 http://localhost:3002 运行"
echo "- 后端已在 http://localhost:8081 运行"
echo "- 所有TypeScript编译错误已修复"
echo "- 可以开始手动验证前端功能"
