# 前端问题:(需验证前端问题是否ok，而不仅仅只验证后端)
## 用户管理
- 按创建时间搜索不好使.
- 点击列表页禁用/启动按钮不生效, 后端api接口ok,但是前端列表没有更新
- 删除用户调用api接口ok,但是前端列表没有更新

## 角色管理
- 新增角色点击创建,如果角色存在会返回:{"error":"角色编码已存在"},但是ui交互没有提示用户角色已存在
- 权限配置,点击了确定按钮,数据返回了{message: "权限分配成功", success: true}, 但是列表页没有更新权限配置

## 商品管理
- 增加商品,创建商品报错:POST http://localhost:8081/api/v1/products 400 (Bad Request). 返回: {"Key: 'CreateProductRequest.Description' Error:Field validation for 'Description' failed on the 'required' tag"}
- 添加分类,分类编码调整为下拉选择框. 创建时报错:http://localhost:8081/api/v1/categories 400 (Bad Request),返回: {"Key: 'CreateCategoryRequest.Level' Error:Field validation for 'Level' failed on the 'required' tag\nKey: 'CreateCategoryRequest.SortOrder' Error:Field validation for 'SortOrder' failed on the 'required' tag"}
- 商品审核,“待审核商品” 是个什么功能,这个页面你是如何设计的.请选分析原始需求sell_flower.md,并给出优化


